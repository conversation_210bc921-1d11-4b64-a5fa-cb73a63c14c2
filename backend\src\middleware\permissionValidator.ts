/**
 * 权限验证中间件
 * 提供更严格的权限验证和安全检查
 */

import { Request, Response, NextFunction } from 'express'
import { AuthenticatedRequest } from '../types/auth'
import { createError } from './errorHandler'
import { UserRole } from '../models/UserRole'
import { User } from '../models/User'

/**
 * 权限验证配置接口
 */
interface PermissionValidationConfig {
  permissions?: string[]
  roles?: string[]
  requireAll?: boolean // 是否需要全部权限/角色
  allowSelf?: boolean // 是否允许用户访问自己的资源
  resourceOwnerField?: string // 资源所有者字段名
  skipIfOwner?: boolean // 如果是资源所有者则跳过权限检查
}

/**
 * 增强的权限验证中间件
 * @param config 权限验证配置
 * @returns Express中间件函数
 */
export const validatePermissions = (config: PermissionValidationConfig) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id
      const { permissions, roles, requireAll = false, allowSelf = false, resourceOwnerField, skipIfOwner = false } = config

      // 检查是否为资源所有者
      if (allowSelf || skipIfOwner) {
        const isOwner = await checkResourceOwnership(req, userId, resourceOwnerField)
        
        if (isOwner) {
          if (skipIfOwner) {
            return next() // 跳过权限检查
          }
          if (allowSelf && !permissions && !roles) {
            return next() // 允许访问自己的资源
          }
        }
      }

      // 验证用户是否存在且活跃
      const user = await User.findByPk(userId)
      if (!user || !user.isActive) {
        throw createError(403, '用户账户已被禁用', 'USER_INACTIVE')
      }

      // 检查角色权限
      if (roles && roles.length > 0) {
        const hasRequiredRoles = await checkUserRoles(userId, roles, requireAll)
        if (!hasRequiredRoles) {
          throw createError(403, `需要以下角色${requireAll ? '全部' : '之一'}: ${roles.join(', ')}`, 'INSUFFICIENT_ROLES')
        }
      }

      // 检查具体权限
      if (permissions && permissions.length > 0) {
        const hasRequiredPermissions = await checkUserPermissions(userId, permissions, requireAll)
        if (!hasRequiredPermissions) {
          throw createError(403, `缺少以下权限${requireAll ? '全部' : '之一'}: ${permissions.join(', ')}`, 'INSUFFICIENT_PERMISSIONS')
        }
      }

      // 记录权限检查日志（可选）
      if (process.env.NODE_ENV === 'development') {
        console.log(`权限验证通过 - 用户: ${userId}, 权限: ${permissions?.join(',') || 'N/A'}, 角色: ${roles?.join(',') || 'N/A'}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 检查用户角色
 * @param userId 用户ID
 * @param requiredRoles 需要的角色列表
 * @param requireAll 是否需要全部角色
 * @returns 是否满足角色要求
 */
async function checkUserRoles(userId: number, requiredRoles: string[], requireAll: boolean): Promise<boolean> {
  try {
    const userRoles = await UserRole.getUserRoles(userId)
    const userRoleNames = userRoles.map(role => role.name)

    if (requireAll) {
      return requiredRoles.every(role => userRoleNames.includes(role))
    } else {
      return requiredRoles.some(role => userRoleNames.includes(role))
    }
  } catch (error) {
    console.error('检查用户角色失败:', error)
    return false
  }
}

/**
 * 检查用户权限
 * @param userId 用户ID
 * @param requiredPermissions 需要的权限列表
 * @param requireAll 是否需要全部权限
 * @returns 是否满足权限要求
 */
async function checkUserPermissions(userId: number, requiredPermissions: string[], requireAll: boolean): Promise<boolean> {
  try {
    if (requireAll) {
      // 检查是否拥有全部权限
      for (const permission of requiredPermissions) {
        const hasPermission = await UserRole.hasPermission(userId, permission)
        if (!hasPermission) {
          return false
        }
      }
      return true
    } else {
      // 检查是否拥有任意一个权限
      for (const permission of requiredPermissions) {
        const hasPermission = await UserRole.hasPermission(userId, permission)
        if (hasPermission) {
          return true
        }
      }
      return false
    }
  } catch (error) {
    console.error('检查用户权限失败:', error)
    return false
  }
}

/**
 * 检查资源所有权
 * @param req 请求对象
 * @param userId 用户ID
 * @param ownerField 所有者字段名
 * @returns 是否为资源所有者
 */
async function checkResourceOwnership(req: AuthenticatedRequest, userId: number, ownerField?: string): Promise<boolean> {
  try {
    // 如果没有指定所有者字段，检查URL参数中的userId
    if (!ownerField) {
      const resourceUserId = parseInt(req.params.userId || req.params.id || '0')
      return resourceUserId === userId
    }

    // 如果指定了所有者字段，需要从数据库查询资源
    // 这里需要根据具体的资源类型来实现
    // 暂时返回false，具体实现需要根据业务需求
    return false
  } catch (error) {
    console.error('检查资源所有权失败:', error)
    return false
  }
}

/**
 * 权限验证装饰器工厂
 * 用于快速创建常用的权限验证中间件
 */
export const PermissionValidators = {
  /**
   * 需要管理员权限
   */
  requireAdmin: () => validatePermissions({
    roles: ['super_admin', 'admin']
  }),

  /**
   * 需要编辑权限
   */
  requireEditor: () => validatePermissions({
    roles: ['super_admin', 'admin', 'editor']
  }),

  /**
   * 需要特定权限
   */
  requirePermission: (permission: string) => validatePermissions({
    permissions: [permission]
  }),

  /**
   * 需要任意一个权限
   */
  requireAnyPermission: (permissions: string[]) => validatePermissions({
    permissions,
    requireAll: false
  }),

  /**
   * 需要全部权限
   */
  requireAllPermissions: (permissions: string[]) => validatePermissions({
    permissions,
    requireAll: true
  }),

  /**
   * 允许访问自己的资源或需要特定权限
   */
  requireOwnershipOrPermission: (permission: string) => validatePermissions({
    permissions: [permission],
    allowSelf: true,
    skipIfOwner: true
  }),

  /**
   * 需要特定角色或是资源所有者
   */
  requireRoleOrOwnership: (roles: string[]) => validatePermissions({
    roles,
    allowSelf: true,
    skipIfOwner: true
  })
}

/**
 * 权限检查结果缓存
 * 用于提高权限检查性能
 */
class PermissionCache {
  private cache = new Map<string, { result: boolean; timestamp: number }>()
  private readonly TTL = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取缓存的权限检查结果
   */
  get(key: string): boolean | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }

    return cached.result
  }

  /**
   * 设置权限检查结果缓存
   */
  set(key: string, result: boolean): void {
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 清除用户相关的缓存
   */
  clearUserCache(userId: number): void {
    for (const [key] of this.cache) {
      if (key.startsWith(`user_${userId}_`)) {
        this.cache.delete(key)
      }
    }
  }
}

export const permissionCache = new PermissionCache()

/**
 * 权限检查中间件（带缓存）
 * @param config 权限验证配置
 * @returns Express中间件函数
 */
export const validatePermissionsWithCache = (config: PermissionValidationConfig) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id
      const cacheKey = `user_${userId}_${JSON.stringify(config)}`

      // 检查缓存
      const cachedResult = permissionCache.get(cacheKey)
      if (cachedResult !== null) {
        if (cachedResult) {
          return next()
        } else {
          throw createError(403, '权限不足', 'INSUFFICIENT_PERMISSIONS')
        }
      }

      // 执行权限检查
      const originalNext = next
      let hasPermission = false

      const mockNext = (error?: any) => {
        if (!error) {
          hasPermission = true
          permissionCache.set(cacheKey, true)
          originalNext()
        } else {
          permissionCache.set(cacheKey, false)
          originalNext(error)
        }
      }

      await validatePermissions(config)(req, res, mockNext)
    } catch (error) {
      next(error)
    }
  }
}
