<template>
  <header class="frontend-navbar">
    <div class="container">
      <!-- 品牌区域 -->
      <div class="nav-brand">
        <RouterLink to="/" class="brand-link">
          <h1 class="brand-title">个人博客</h1>
        </RouterLink>
      </div>

      <!-- 主导航菜单 -->
      <nav class="nav-menu">
        <RouterLink to="/" class="nav-link">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </RouterLink>
        <RouterLink to="/article" class="nav-link">
          <el-icon><Document /></el-icon>
          <span>文章</span>
        </RouterLink>
        <RouterLink to="/posts" class="nav-link">
          <el-icon><ChatLineRound /></el-icon>
          <span>说说</span>
        </RouterLink>
        <RouterLink to="/category" class="nav-link">
          <el-icon><FolderOpened /></el-icon>
          <span>分类</span>
        </RouterLink>
        <RouterLink to="/tags" class="nav-link">
          <el-icon><PriceTag /></el-icon>
          <span>标签</span>
        </RouterLink>
      </nav>

      <!-- 右侧功能区 -->
      <div class="nav-actions">
        <!-- 搜索框 -->
        <div class="search-wrapper">
          <SearchBar />
        </div>

        <!-- 通知图标 -->
        <NotificationBadge v-if="authStore.isAuthenticated" />

        <!-- 主题切换 -->
        <ThemeToggle mode="icon" />

        <!-- 用户菜单或登录按钮 -->
        <UserDropdown v-if="authStore.isAuthenticated" />
        <RouterLink v-else to="/login" class="login-btn">
          <el-icon><User /></el-icon>
          <span>登录</span>
        </RouterLink>

        <!-- 开发环境链接 -->
        <RouterLink 
          v-if="isDevelopment" 
          to="/design-system" 
          class="nav-link dev-link"
          title="设计系统"
        >
          <el-icon><Tools /></el-icon>
        </RouterLink>
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-btn" @click="toggleMobileMenu">
        <el-icon>
          <Menu v-if="!mobileMenuOpen" />
          <Close v-else />
        </el-icon>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div v-show="mobileMenuOpen" class="mobile-menu">
      <div class="mobile-nav-links">
        <RouterLink to="/" class="mobile-nav-link" @click="closeMobileMenu">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </RouterLink>
        <RouterLink to="/article" class="mobile-nav-link" @click="closeMobileMenu">
          <el-icon><Document /></el-icon>
          <span>文章</span>
        </RouterLink>
        <RouterLink to="/posts" class="mobile-nav-link" @click="closeMobileMenu">
          <el-icon><ChatLineRound /></el-icon>
          <span>说说</span>
        </RouterLink>
        <RouterLink to="/category" class="mobile-nav-link" @click="closeMobileMenu">
          <el-icon><FolderOpened /></el-icon>
          <span>分类</span>
        </RouterLink>
        <RouterLink to="/tags" class="mobile-nav-link" @click="closeMobileMenu">
          <el-icon><PriceTag /></el-icon>
          <span>标签</span>
        </RouterLink>
        
        <!-- 移动端用户操作 -->
        <div class="mobile-user-actions">
          <template v-if="authStore.isAuthenticated">
            <RouterLink to="/admin" class="mobile-nav-link" @click="closeMobileMenu">
              <el-icon><Setting /></el-icon>
              <span>管理后台</span>
            </RouterLink>
            <button @click="handleLogout" class="mobile-nav-link logout-btn">
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </button>
          </template>
          <RouterLink v-else to="/login" class="mobile-nav-link" @click="closeMobileMenu">
            <el-icon><User /></el-icon>
            <span>登录</span>
          </RouterLink>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import SearchBar from './SearchBar.vue'
import ThemeToggle from './ThemeToggle.vue'
import NotificationBadge from '@/components/notification/NotificationBadge.vue'
import UserDropdown from './UserDropdown.vue'

const router = useRouter()
const authStore = useAuthStore()

// 移动端菜单状态
const mobileMenuOpen = ref(false)

// 判断当前是否为开发环境
const isDevelopment = import.meta.env.DEV

// 移动端菜单控制
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 处理用户登出
const handleLogout = () => {
  authStore.logout()
  router.push('/')
  closeMobileMenu()
}
</script>

<style scoped>
.frontend-navbar {
  background-color: var(--color-nav-bg);
  border-bottom: 1px solid var(--color-nav-border);
  position: relative;
}

.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

/* 品牌区域 */
.nav-brand {
  flex-shrink: 0;
}

.brand-link {
  text-decoration: none;
  color: var(--color-text-primary);
  transition: color var(--transition-fast);
}

.brand-link:hover {
  color: var(--color-primary);
}

.brand-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 主导航菜单 */
.nav-menu {
  display: flex;
  gap: var(--spacing-6);
  align-items: center;
  flex: 1;
  justify-content: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  text-decoration: none;
  color: var(--color-nav-text);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--color-nav-text-hover);
  background-color: var(--color-surface-hover);
}

.nav-link.router-link-active {
  color: var(--color-nav-text-active);
  background-color: var(--color-primary-light);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

/* 右侧功能区 */
.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex-shrink: 0;
}

.search-wrapper {
  min-width: 200px;
}

.login-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: background-color var(--transition-fast);
}

.login-btn:hover {
  background-color: var(--color-primary-hover);
}

.dev-link {
  background-color: var(--color-warning-bg);
  color: var(--color-warning);
  border: 1px solid var(--color-warning-border);
}

.dev-link:hover {
  background-color: var(--color-warning);
  color: var(--color-text-inverse);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: var(--color-surface-hover);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.mobile-menu-btn:hover {
  background-color: var(--color-surface-active);
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-nav-bg);
  border-bottom: 1px solid var(--color-nav-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.mobile-nav-links {
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  text-decoration: none;
  color: var(--color-nav-text);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  font-size: var(--font-size-sm);
  font-family: inherit;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.mobile-nav-link:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-nav-text-hover);
}

.mobile-nav-link.router-link-active {
  background-color: var(--color-primary-light);
  color: var(--color-nav-text-active);
}

.mobile-user-actions {
  border-top: 1px solid var(--color-nav-border);
  padding-top: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.logout-btn {
  color: var(--color-error);
}

.logout-btn:hover {
  background-color: var(--color-error-bg);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .search-wrapper {
    min-width: 150px;
  }
  
  .nav-menu {
    gap: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .nav-menu,
  .search-wrapper {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-actions {
    gap: var(--spacing-2);
  }
  
  .brand-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-3);
  }
  
  .nav-actions {
    gap: var(--spacing-1);
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .frontend-navbar {
    border-bottom-width: 2px;
  }
  
  .brand-title {
    -webkit-text-fill-color: unset;
    background: unset;
    color: var(--color-text-primary);
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .nav-link::after {
    transition: none;
  }
}
</style>
