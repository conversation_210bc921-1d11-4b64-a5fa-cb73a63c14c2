# 智能导航栏系统

## 概述

本项目实现了一个智能的导航栏系统，能够根据用户的权限和当前访问的页面自动切换显示前台导航栏或管理后台导航栏。

## 组件结构

### 主要组件

1. **AppNavbar.vue** - 主导航栏组件
   - 根据当前路由和用户权限自动选择显示的导航栏类型
   - 作为导航栏的入口组件

2. **FrontendNavbar.vue** - 前台导航栏
   - 适用于博客前台页面
   - 包含首页、文章、说说、分类、标签等导航
   - 响应式设计，支持移动端

3. **AdminNavbar.vue** - 管理后台导航栏
   - 适用于管理后台页面
   - 包含快速导航、搜索、面包屑等功能
   - 渐变背景设计，专业的管理界面风格

4. **UserDropdown.vue** - 用户下拉菜单
   - 显示用户信息、角色、权限
   - 提供个人功能和管理功能的快速入口
   - 根据用户权限动态显示菜单项

5. **AdminBreadcrumb.vue** - 管理后台面包屑导航
   - 显示当前页面在管理后台中的位置
   - 支持点击导航到上级页面

## 切换逻辑

### 自动切换规则

导航栏的显示遵循以下逻辑：

1. **管理后台导航栏显示条件**：
   - 当前路由以 `/admin` 开头
   - 或当前路由为 `/settings`
   - 或当前路由名称为 `MyAuditLogs`
   - **且** 用户拥有管理权限（角色为 super_admin、admin 或 editor）

2. **前台导航栏显示条件**：
   - 所有其他情况
   - 包括用户无管理权限但访问管理路由的情况

### 权限检查

- 使用 `useGlobalMenu()` 组合式函数中的 `hasAdminAccess` 来判断用户是否有管理权限
- 权限检查基于用户的角色：super_admin、admin、editor 拥有管理权限

## 功能特性

### 前台导航栏特性

- **主导航菜单**：首页、文章、说说、分类、标签
- **搜索功能**：集成搜索栏组件
- **用户操作**：登录/用户菜单、通知、主题切换
- **响应式设计**：移动端折叠菜单
- **开发模式**：开发环境显示设计系统链接

### 管理后台导航栏特性

- **品牌标识**：管理后台标题和图标
- **面包屑导航**：显示当前页面位置
- **快速导航**：常用管理功能的快速入口
- **搜索功能**：管理菜单搜索
- **用户信息**：用户下拉菜单
- **返回前台**：快速返回前台的按钮
- **渐变背景**：专业的视觉设计

### 用户下拉菜单特性

- **用户信息展示**：头像、用户名、邮箱、角色标签
- **个人功能**：个人资料、我的文章、我的说说、操作日志
- **管理功能**：根据权限显示管理后台、系统设置
- **偏好设置**：主题和个人偏好
- **退出登录**：安全退出功能

## 使用方法

### 基本使用

在 `App.vue` 中使用主导航栏组件：

```vue
<template>
  <div id="app">
    <AppNavbar />
    <main>
      <RouterView />
    </main>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import AppNavbar from '@/components/common/AppNavbar.vue'
</script>
```

### 权限配置

确保在路由配置中正确设置权限要求：

```typescript
{
  path: '/admin/users',
  name: 'AdminUsers',
  component: () => import('@/views/admin/UserManagement.vue'),
  meta: {
    requiresAuth: true,
    title: '用户管理',
    permissions: [USER_PERMISSIONS.LIST, USER_PERMISSIONS.READ],
    roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
  }
}
```

### 菜单配置

在 `@/constants/menu.ts` 中配置管理后台菜单：

```typescript
export const ADMIN_MENU: MenuItem[] = [
  {
    id: 'users',
    title: '用户管理',
    icon: 'User',
    path: '/admin/users',
    permissions: [USER_PERMISSIONS.LIST],
    roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
  }
]
```

## 样式定制

### CSS 变量

在 `@/styles/variables.css` 中定义了导航栏相关的 CSS 变量：

```css
/* 导航栏相关变量 */
--color-nav-bg: rgba(255, 255, 255, 0.95);
--color-nav-border: rgba(0, 0, 0, 0.08);
--color-nav-text: var(--color-text-primary);
--color-nav-text-hover: var(--color-primary);
--color-nav-text-active: var(--color-primary);
--color-primary-light: rgba(59, 130, 246, 0.1);
```

### 主题适配

导航栏支持明暗主题切换，会自动适配当前主题的颜色方案。

## 测试和演示

访问 `/navbar-demo` 页面可以查看导航栏系统的演示和测试功能：

- 查看当前导航栏状态
- 测试不同页面的导航切换
- 查看用户权限信息
- 理解切换逻辑

## 注意事项

1. **权限依赖**：导航栏的显示依赖于权限系统，确保权限系统正常工作
2. **路由配置**：确保路由的 meta 信息正确配置
3. **组件依赖**：确保所有依赖的组件（SearchBar、ThemeToggle、NotificationBadge等）存在
4. **CSS 变量**：确保 CSS 变量在样式文件中正确定义

## 扩展开发

### 添加新的导航项

1. 在 `@/constants/menu.ts` 中添加菜单配置
2. 在相应的导航栏组件中添加导航项
3. 配置相应的权限要求

### 自定义导航栏

可以创建新的导航栏组件，并在 `AppNavbar.vue` 中添加切换逻辑。

### 移动端适配

导航栏已经包含了基本的移动端适配，可以根据需要进一步优化移动端体验。
