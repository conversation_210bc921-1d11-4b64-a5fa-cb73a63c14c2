<template>
  <div class="app-navbar">
    <!-- 前台导航栏 -->
    <FrontendNavbar v-if="!isAdminRoute" />
    
    <!-- 管理后台导航栏 -->
    <AdminNavbar v-else-if="hasAdminAccess" />
    
    <!-- 无权限访问管理后台时显示前台导航 -->
    <FrontendNavbar v-else />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalMenu } from '@/composables/useMenu'
import FrontendNavbar from './FrontendNavbar.vue'
import AdminNavbar from './AdminNavbar.vue'

const route = useRoute()
const { hasAdminAccess } = useGlobalMenu()

// 判断当前是否在管理后台路由
const isAdminRoute = computed(() => {
  return route.path.startsWith('/admin') || 
         route.path.startsWith('/settings') ||
         route.name === 'MyAuditLogs'
})
</script>

<style scoped>
.app-navbar {
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  background-color: var(--color-nav-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
</style>
