<template>
  <div id="app">
    <!-- 智能导航栏组件 -->
    <AppNavbar />
    <!-- 页面主体内容区域 -->
    <main class="main-content">
      <!-- 路由视图容器 -->
      <RouterView />
    </main>
    <!-- 页面底部组件 -->
    <Footer />
    
  </div>
</template>

<script setup lang="ts">
/**
 * 应用根组件
 * 负责组织页面整体结构，包含头部、主体内容、底部以及全局通用组件
 * 
 * 组件结构说明：
 * - AppNavbar: 智能导航栏（根据路由和权限自动切换前台/后台导航）
 * - RouterView: 路由内容显示区域
 * - Footer: 页面底部信息区域
 */

import { RouterView } from 'vue-router'
import AppNavbar from '@/components/common/AppNavbar.vue'
import Footer from '@/components/common/Footer.vue'

</script>

<style scoped>
/**
 * 应用根容器样式
 * 设置最小高度为视窗高度，采用弹性布局实现页脚始终在底部
 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

/**
 * 主体内容区域样式
 * 使用弹性布局占据剩余空间，设置响应式内边距和最大宽度
 */
.main-content {
  flex: 1;
  width: 100%;
}

/**
 * 平板及以下设备样式调整
 * 减小内边距以适应小屏幕显示
 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4);
  }
}

/**
 * 手机设备样式调整
 * 进一步减小内边距优化移动端体验
 */
@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-3);
  }
}
</style>