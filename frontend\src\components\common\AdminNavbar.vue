<template>
  <header class="admin-navbar">
    <div class="container">
      <!-- 左侧区域 -->
      <div class="nav-left">
        <!-- 品牌区域 -->
        <div class="nav-brand">
          <RouterLink to="/admin" class="brand-link">
            <el-icon class="brand-icon"><Setting /></el-icon>
            <h1 class="brand-title">管理后台</h1>
          </RouterLink>
        </div>

        <!-- 面包屑导航 -->
        <AdminBreadcrumb />
      </div>

      <!-- 中间区域 - 快速导航 -->
      <div class="nav-center">
        <div class="quick-nav">
          <RouterLink 
            v-for="item in quickNavItems" 
            :key="item.id"
            :to="item.path"
            class="quick-nav-item"
            :title="item.title"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span class="quick-nav-text">{{ item.title }}</span>
          </RouterLink>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="nav-right">
        <!-- 搜索框 -->
        <div class="search-wrapper">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索菜单..."
            prefix-icon="Search"
            size="small"
            clearable
            @input="handleSearch"
            class="admin-search"
          />
        </div>

        <!-- 通知图标 -->
        <NotificationBadge />

        <!-- 返回前台按钮 -->
        <RouterLink to="/" class="frontend-btn" title="返回前台">
          <el-icon><House /></el-icon>
          <span>前台</span>
        </RouterLink>

        <!-- 主题切换 -->
        <ThemeToggle mode="icon" />

        <!-- 用户菜单 -->
        <UserDropdown />

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <el-icon>
            <Menu v-if="!mobileMenuOpen" />
            <Close v-else />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 搜索结果下拉 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <div class="search-results-container">
        <div class="search-results-header">
          <span>搜索结果</span>
          <el-button text @click="clearSearch">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="search-results-list">
          <RouterLink
            v-for="result in searchResults"
            :key="result.id"
            :to="result.path || '#'"
            class="search-result-item"
            @click="clearSearch"
          >
            <el-icon v-if="result.icon">
              <component :is="result.icon" />
            </el-icon>
            <span>{{ result.title }}</span>
          </RouterLink>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div v-show="mobileMenuOpen" class="mobile-menu">
      <div class="mobile-nav-content">
        <!-- 移动端快速导航 -->
        <div class="mobile-quick-nav">
          <RouterLink
            v-for="item in quickNavItems"
            :key="item.id"
            :to="item.path"
            class="mobile-quick-nav-item"
            @click="closeMobileMenu"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.title }}</span>
          </RouterLink>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions">
          <RouterLink to="/" class="mobile-action-btn" @click="closeMobileMenu">
            <el-icon><House /></el-icon>
            <span>返回前台</span>
          </RouterLink>
          <button @click="handleLogout" class="mobile-action-btn logout-btn">
            <el-icon><SwitchButton /></el-icon>
            <span>退出登录</span>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalMenu } from '@/composables/useMenu'
import AdminBreadcrumb from './AdminBreadcrumb.vue'
import ThemeToggle from './ThemeToggle.vue'
import NotificationBadge from '@/components/notification/NotificationBadge.vue'
import UserDropdown from './UserDropdown.vue'

const router = useRouter()
const authStore = useAuthStore()
const { adminMenu, searchMenuItems } = useGlobalMenu()

// 响应式状态
const mobileMenuOpen = ref(false)
const searchKeyword = ref('')
const searchResults = ref<any[]>([])

// 快速导航项目（常用功能）
const quickNavItems = computed(() => {
  const items = [
    { id: 'articles', title: '文章', icon: 'Document', path: '/admin/articles' },
    { id: 'categories', title: '分类', icon: 'FolderOpened', path: '/admin/categories' },
    { id: 'tags', title: '标签', icon: 'PriceTag', path: '/admin/tags' },
    { id: 'comments', title: '评论', icon: 'ChatDotRound', path: '/admin/comments' },
    { id: 'media', title: '媒体', icon: 'Picture', path: '/admin/media' },
    { id: 'users', title: '用户', icon: 'User', path: '/admin/users' }
  ]
  
  // 根据用户权限过滤快速导航项
  return items.filter(item => {
    const menuItem = adminMenu.value.find(menu => 
      menu.path === item.path || 
      (menu.children && menu.children.some(child => child.path === item.path))
    )
    return menuItem !== undefined
  })
})

// 搜索处理
const handleSearch = (keyword: string) => {
  if (!keyword.trim()) {
    searchResults.value = []
    return
  }
  
  searchResults.value = searchMenuItems(keyword.trim())
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
}

// 移动端菜单控制
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 处理用户登出
const handleLogout = () => {
  authStore.logout()
  router.push('/')
  closeMobileMenu()
}
</script>

<style scoped>
.admin-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

/* 左侧区域 */
.nav-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  flex: 1;
}

.nav-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  text-decoration: none;
  color: white;
  transition: opacity var(--transition-fast);
}

.brand-link:hover {
  opacity: 0.8;
}

.brand-icon {
  font-size: 24px;
}

.brand-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

/* 中间区域 */
.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.quick-nav {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.quick-nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.quick-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.quick-nav-item.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.quick-nav-text {
  white-space: nowrap;
}

/* 右侧区域 */
.nav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.search-wrapper {
  position: relative;
}

.admin-search {
  width: 200px;
}

.admin-search :deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.admin-search :deep(.el-input__inner) {
  color: white;
}

.admin-search :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6);
}

.frontend-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  transition: background-color var(--transition-fast);
}

.frontend-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.search-results-container {
  padding: var(--spacing-2);
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  border-bottom: 1px solid var(--color-border);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.search-results-list {
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  text-decoration: none;
  color: var(--color-text-primary);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.search-result-item:hover {
  background-color: var(--color-surface-hover);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.mobile-menu-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.mobile-nav-content {
  padding: var(--spacing-4);
}

.mobile-quick-nav {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.mobile-quick-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-3);
  text-decoration: none;
  color: var(--color-text-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.mobile-quick-nav-item:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-primary);
}

.mobile-actions {
  border-top: 1px solid var(--color-border);
  padding-top: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.mobile-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  text-decoration: none;
  color: var(--color-text-primary);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  background: none;
  border: none;
  font-size: var(--font-size-sm);
  font-family: inherit;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.mobile-action-btn:hover {
  background-color: var(--color-surface-hover);
}

.logout-btn {
  color: var(--color-error);
}

.logout-btn:hover {
  background-color: var(--color-error-bg);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .quick-nav-text {
    display: none;
  }
  
  .search-wrapper {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .nav-center,
  .search-wrapper {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-right {
    gap: var(--spacing-2);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-3);
  }
  
  .nav-left {
    gap: var(--spacing-3);
  }
  
  .brand-title {
    font-size: var(--font-size-base);
  }
}
</style>
