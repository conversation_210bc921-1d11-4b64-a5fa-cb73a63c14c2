<template>
  <div class="admin-breadcrumb">
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumbItems" 
        :key="item.id"
        :to="index < breadcrumbItems.length - 1 ? item.path : undefined"
        class="breadcrumb-item"
      >
        <el-icon v-if="item.icon && index === 0">
          <component :is="item.icon" />
        </el-icon>
        <span>{{ item.title }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalMenu } from '@/composables/useMenu'

const route = useRoute()
const { breadcrumb } = useGlobalMenu()

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  const items = [...breadcrumb.value]
  
  // 如果没有面包屑数据，根据路由生成基础面包屑
  if (items.length === 0) {
    const pathSegments = route.path.split('/').filter(Boolean)
    
    if (pathSegments[0] === 'admin') {
      items.push({
        id: 'admin',
        title: '管理后台',
        icon: 'Setting',
        path: '/admin'
      })
      
      // 根据路由路径生成面包屑
      if (pathSegments.length > 1) {
        const routeTitle = getRouteTitleByPath(route.path)
        if (routeTitle) {
          items.push({
            id: route.name as string || 'current',
            title: routeTitle,
            path: route.path
          })
        }
      }
    }
  }
  
  return items
})

// 根据路径获取路由标题
const getRouteTitleByPath = (path: string): string => {
  const routeTitleMap: Record<string, string> = {
    '/admin': '仪表盘',
    '/admin/articles': '文章管理',
    '/admin/articles/new': '新建文章',
    '/admin/categories': '分类管理',
    '/admin/tags': '标签管理',
    '/admin/comments': '评论管理',
    '/admin/posts': '说说管理',
    '/admin/media': '媒体管理',
    '/admin/users': '用户管理',
    '/admin/roles': '角色管理',
    '/admin/user-roles': '用户角色分配',
    '/admin/notifications': '通知中心',
    '/admin/audit-logs': '审计日志',
    '/settings': '系统设置',
    '/my/audit-logs': '我的操作日志'
  }
  
  // 精确匹配
  if (routeTitleMap[path]) {
    return routeTitleMap[path]
  }
  
  // 模糊匹配（用于动态路由）
  if (path.includes('/admin/articles/') && path.includes('/edit')) {
    return '编辑文章'
  }
  
  // 默认返回路由名称或路径的最后一段
  const segments = path.split('/').filter(Boolean)
  return segments[segments.length - 1] || '未知页面'
}
</script>

<style scoped>
.admin-breadcrumb {
  flex: 1;
  min-width: 0;
}

.breadcrumb {
  color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 自定义面包屑样式 */
:deep(.el-breadcrumb__item) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-breadcrumb__item:last-child) {
  color: white;
  font-weight: var(--font-weight-medium);
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: inherit;
  font-weight: inherit;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner:hover) {
  color: white;
}

:deep(.el-breadcrumb__separator) {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 var(--spacing-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-breadcrumb {
    display: none;
  }
}

@media (max-width: 480px) {
  .breadcrumb-item span {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
