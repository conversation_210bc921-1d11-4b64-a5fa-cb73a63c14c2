<template>
  <el-dropdown 
    trigger="click" 
    placement="bottom-end"
    @command="handleCommand"
    class="user-dropdown"
  >
    <div class="user-trigger">
      <el-avatar 
        :size="32" 
        :src="userAvatar" 
        class="user-avatar"
      >
        <el-icon><User /></el-icon>
      </el-avatar>
      <div class="user-info">
        <span class="username">{{ authStore.user?.username }}</span>
        <span class="user-role">{{ primaryRole }}</span>
      </div>
      <el-icon class="dropdown-arrow">
        <ArrowDown />
      </el-icon>
    </div>
    
    <template #dropdown>
      <el-dropdown-menu class="user-dropdown-menu">
        <!-- 用户信息区域 -->
        <div class="user-header">
          <el-avatar :size="48" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-details">
            <div class="username">{{ authStore.user?.username }}</div>
            <div class="user-email">{{ authStore.user?.email }}</div>
            <div class="user-roles">
              <el-tag 
                v-for="role in userRoles" 
                :key="role"
                :type="getRoleTagType(role)"
                size="small"
              >
                {{ getRoleDisplayName(role) }}
              </el-tag>
            </div>
          </div>
        </div>

        <el-divider style="margin: 8px 0;" />

        <!-- 个人功能菜单 -->
        <el-dropdown-item command="profile" :icon="User">
          个人资料
        </el-dropdown-item>
        
        <el-dropdown-item command="my-articles" :icon="Document">
          我的文章
        </el-dropdown-item>
        
        <el-dropdown-item command="my-posts" :icon="ChatLineRound">
          我的说说
        </el-dropdown-item>
        
        <el-dropdown-item command="my-audit-logs" :icon="List">
          操作日志
        </el-dropdown-item>

        <el-divider style="margin: 8px 0;" />

        <!-- 管理功能菜单（根据权限显示） -->
        <el-dropdown-item 
          v-if="hasAdminAccess" 
          command="admin" 
          :icon="Setting"
        >
          管理后台
        </el-dropdown-item>
        
        <el-dropdown-item 
          v-if="hasAdminAccess" 
          command="settings" 
          :icon="Tools"
        >
          系统设置
        </el-dropdown-item>

        <el-divider v-if="hasAdminAccess" style="margin: 8px 0;" />

        <!-- 主题和偏好设置 -->
        <el-dropdown-item command="preferences" :icon="Setting">
          偏好设置
        </el-dropdown-item>

        <el-divider style="margin: 8px 0;" />

        <!-- 退出登录 -->
        <el-dropdown-item 
          command="logout" 
          :icon="SwitchButton"
          class="logout-item"
        >
          退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalMenu } from '@/composables/useMenu'

const router = useRouter()
const authStore = useAuthStore()
const { hasAdminAccess, userRoles } = useGlobalMenu()

// 计算用户头像URL
const userAvatar = computed(() => {
  // 这里可以根据用户信息返回头像URL
  // 暂时返回空，使用默认头像
  return ''
})

// 计算主要角色
const primaryRole = computed(() => {
  if (userRoles.value.includes('super_admin')) return '超级管理员'
  if (userRoles.value.includes('admin')) return '管理员'
  if (userRoles.value.includes('editor')) return '编辑者'
  if (userRoles.value.includes('user')) return '用户'
  return '访客'
})

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'super_admin': return 'danger'
    case 'admin': return 'warning'
    case 'editor': return 'primary'
    case 'user': return 'info'
    default: return 'info'
  }
}

// 获取角色显示名称
const getRoleDisplayName = (role: string) => {
  switch (role) {
    case 'super_admin': return '超级管理员'
    case 'admin': return '管理员'
    case 'editor': return '编辑者'
    case 'user': return '用户'
    default: return role
  }
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'my-articles':
      router.push('/my/articles')
      break
    case 'my-posts':
      router.push('/my/posts')
      break
    case 'my-audit-logs':
      router.push('/my/audit-logs')
      break
    case 'admin':
      router.push('/admin')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'preferences':
      // 打开偏好设置对话框或页面
      console.log('打开偏好设置')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理用户登出
const handleLogout = () => {
  authStore.logout()
  router.push('/')
}
</script>

<style scoped>
.user-dropdown {
  cursor: pointer;
}

.user-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.user-trigger:hover {
  background-color: var(--color-surface-hover, rgba(255, 255, 255, 0.1));
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.username {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
  white-space: nowrap;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform var(--transition-fast);
}

.user-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.user-dropdown-menu {
  min-width: 280px;
  padding: var(--spacing-2);
}

.user-header {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--color-surface-light);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-2);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-details .username {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
  max-width: none;
}

.user-email {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
  word-break: break-all;
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
}

/* 下拉菜单项样式 */
:deep(.el-dropdown-menu__item) {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-1);
  transition: all var(--transition-fast);
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--color-surface-hover);
}

:deep(.el-dropdown-menu__item.logout-item) {
  color: var(--color-error);
}

:deep(.el-dropdown-menu__item.logout-item:hover) {
  background-color: var(--color-error-bg);
  color: var(--color-error);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info {
    display: none;
  }
  
  .dropdown-arrow {
    display: none;
  }
  
  .user-dropdown-menu {
    min-width: 240px;
  }
}

@media (max-width: 480px) {
  .user-dropdown-menu {
    min-width: 200px;
  }
  
  .user-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}
</style>
