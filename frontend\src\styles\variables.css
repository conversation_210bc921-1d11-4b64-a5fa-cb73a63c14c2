/*
  基于 Element Plus 的设计系统变量
  该文件定义了与 Element Plus 兼容的设计令牌，包括颜色、字体、间距、阴影、过渡等，
  确保与 Element Plus 组件的完美集成。
*/

:root {
  /* ================================
     * 基础调色板 - 保持与原设计一致
     * ================================ */

  /* 主色调 - 蓝色系，从浅到深共9个层级 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* 成功状态色 - 绿色系 */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  /* 警告状态色 - 黄/橙色系 */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  /* 错误状态色 - 红色系 */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  /* 信息状态色 - 蓝色系 */
  --color-info-50: #f0f9ff;
  --color-info-100: #e0f2fe;
  --color-info-200: #bae6fd;
  --color-info-300: #7dd3fc;
  --color-info-400: #38bdf8;
  --color-info-500: #0ea5e9;
  --color-info-600: #0284c7;
  --color-info-700: #0369a1;
  --color-info-800: #075985;
  --color-info-900: #0c4a6e;

  /* 中性灰度色 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* ================================
     * Element Plus 主题变量映射
     * ================================ */

  /* 主色调映射到 Element Plus */
  --el-color-primary: var(--color-primary-600);
  --el-color-primary-light-3: var(--color-primary-400);
  --el-color-primary-light-5: var(--color-primary-300);
  --el-color-primary-light-7: var(--color-primary-200);
  --el-color-primary-light-8: var(--color-primary-100);
  --el-color-primary-light-9: var(--color-primary-50);
  --el-color-primary-dark-2: var(--color-primary-700);

  /* 状态色映射 */
  --el-color-success: var(--color-success-600);
  --el-color-success-light-3: var(--color-success-400);
  --el-color-success-light-5: var(--color-success-300);
  --el-color-success-light-7: var(--color-success-200);
  --el-color-success-light-8: var(--color-success-100);
  --el-color-success-light-9: var(--color-success-50);
  --el-color-success-dark-2: var(--color-success-700);

  --el-color-warning: var(--color-warning-600);
  --el-color-warning-light-3: var(--color-warning-400);
  --el-color-warning-light-5: var(--color-warning-300);
  --el-color-warning-light-7: var(--color-warning-200);
  --el-color-warning-light-8: var(--color-warning-100);
  --el-color-warning-light-9: var(--color-warning-50);
  --el-color-warning-dark-2: var(--color-warning-700);

  --el-color-danger: var(--color-error-600);
  --el-color-danger-light-3: var(--color-error-400);
  --el-color-danger-light-5: var(--color-error-300);
  --el-color-danger-light-7: var(--color-error-200);
  --el-color-danger-light-8: var(--color-error-100);
  --el-color-danger-light-9: var(--color-error-50);
  --el-color-danger-dark-2: var(--color-error-700);

  --el-color-info: var(--color-info-600);
  --el-color-info-light-3: var(--color-info-400);
  --el-color-info-light-5: var(--color-info-300);
  --el-color-info-light-7: var(--color-info-200);
  --el-color-info-light-8: var(--color-info-100);
  --el-color-info-light-9: var(--color-info-50);
  --el-color-info-dark-2: var(--color-info-700);

  /* ================================
   * 背景和文本颜色映射
   * ================================ */

  /* 背景色 */
  --el-bg-color: #ffffff;
  --el-bg-color-page: var(--color-gray-50);
  --el-bg-color-overlay: rgba(0, 0, 0, 0.5);

  /* 文本色 */
  --el-text-color-primary: var(--color-gray-900);
  --el-text-color-regular: var(--color-gray-700);
  --el-text-color-secondary: var(--color-gray-600);
  --el-text-color-placeholder: var(--color-gray-400);
  --el-text-color-disabled: var(--color-gray-400);

  /* 自定义文本色扩展 */
  --color-text-primary: var(--el-text-color-primary);
  --color-text-secondary: var(--el-text-color-secondary);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-placeholder: var(--el-text-color-placeholder);
  --color-text-disabled: var(--el-text-color-disabled);

  /* 边框色 */
  --el-border-color: var(--color-gray-200);
  --el-border-color-light: var(--color-gray-100);
  --el-border-color-lighter: var(--color-gray-50);
  --el-border-color-extra-light: var(--color-gray-50);
  --el-border-color-dark: var(--color-gray-300);
  --el-border-color-darker: var(--color-gray-400);

  /* 自定义边框色扩展 */
  --color-border: var(--el-border-color);
  --color-border-light: var(--el-border-color-light);
  --color-border-dark: var(--el-border-color-dark);

  /* ================================
   * 字体系统 - 与 Element Plus 兼容
   * ================================ */

  /* 字体族 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-serif: Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-family-mono: 'SFMono-Regular', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Element Plus 字体族映射 */
  --el-font-family: var(--font-family-sans);

  /* 字号定义 */
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-lg: 1.125rem;
  /* 18px */
  --font-size-xl: 1.25rem;
  /* 20px */
  --font-size-2xl: 1.5rem;
  /* 24px */
  --font-size-3xl: 1.875rem;
  /* 30px */
  --font-size-4xl: 2.25rem;
  /* 36px */
  --font-size-5xl: 3rem;
  /* 48px */
  --font-size-6xl: 3.75rem;
  /* 60px */

  /* Element Plus 字号映射 */
  --el-font-size-extra-large: var(--font-size-xl);
  --el-font-size-large: var(--font-size-lg);
  --el-font-size-medium: var(--font-size-base);
  --el-font-size-base: var(--font-size-sm);
  --el-font-size-small: var(--font-size-xs);
  --el-font-size-extra-small: 0.6875rem;
  /* 11px */

  /* 字重定义 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;


  /* 行高定义 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* ================================
   * 间距系统 - 与 Element Plus 兼容
   * ================================ */

  /* 间距单位定义 */
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  /* 4px */
  --spacing-2: 0.5rem;
  /* 8px */
  --spacing-3: 0.75rem;
  /* 12px */
  --spacing-4: 1rem;
  /* 16px */
  --spacing-5: 1.25rem;
  /* 20px */
  --spacing-6: 1.5rem;
  /* 24px */
  --spacing-8: 2rem;
  /* 32px */
  --spacing-10: 2.5rem;
  /* 40px */
  --spacing-12: 3rem;
  /* 48px */
  --spacing-16: 4rem;
  /* 64px */
  --spacing-20: 5rem;
  /* 80px */
  --spacing-24: 6rem;
  /* 96px */
  --spacing-32: 8rem;
  /* 128px */

  /* Element Plus 间距映射 */
  --el-component-size-large: 40px;
  --el-component-size: 32px;
  --el-component-size-small: 24px;

  /* ================================
   * 圆角系统 - 与 Element Plus 兼容
   * ================================ */

  /* 圆角半径定义 */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  /* 2px */
  --radius-base: 0.25rem;
  /* 4px */
  --radius-md: 0.375rem;
  /* 6px */
  --radius-lg: 0.5rem;
  /* 8px */
  --radius-xl: 0.75rem;
  /* 12px */
  --radius-2xl: 1rem;
  /* 16px */
  --radius-3xl: 1.5rem;
  /* 24px */
  --radius-full: 9999px;

  /* Element Plus 圆角映射 */
  --el-border-radius-base: var(--radius-base);
  --el-border-radius-small: var(--radius-sm);
  --el-border-radius-round: var(--radius-full);
  --el-border-radius-circle: 50%;

  /* ================================
   * 阴影系统 - 与 Element Plus 兼容
   * ================================ */

  /* 阴影效果定义 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Element Plus 阴影映射 */
  --el-box-shadow: var(--shadow-base);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-dark: var(--shadow-md);

  /* ================================
   * 过渡动画系统 - 与 Element Plus 兼容
   * ================================ */

  /* 过渡动画时间与缓动函数 */
  --transition-fast: 150ms ease;
  --transition-base: 300ms ease;
  --transition-slow: 500ms ease;

  /* Element Plus 过渡映射 */
  --el-transition-duration: var(--transition-base);
  --el-transition-duration-fast: var(--transition-fast);

  /* 缓动曲线定义 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ================================
   * 层级系统 - 与 Element Plus 兼容
   * ================================ */

  /* 层级（z-index）定义 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Element Plus z-index 映射 */
  --el-index-normal: 1;
  --el-index-top: var(--z-index-dropdown);
  --el-index-popper: var(--z-index-popover);

  /* ================================
   * 响应式系统
   * ================================ */

  /* 响应式断点定义 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 容器最大宽度定义 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* ================================
   * 自定义扩展变量
   * ================================ */

  /* 保留原有的语义化颜色变量以便向后兼容 */
  --color-primary: var(--el-color-primary);
  --color-success: var(--el-color-success);
  --color-warning: var(--el-color-warning);
  --color-error: var(--el-color-danger);
  --color-info: var(--el-color-info);

  /* Z-index 层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* 导航栏相关变量 */
  --color-nav-bg: rgba(255, 255, 255, 0.95);
  --color-nav-border: rgba(0, 0, 0, 0.08);
  --color-nav-text: var(--color-text-primary);
  --color-nav-text-hover: var(--color-primary);
  --color-nav-text-active: var(--color-primary);
  --color-primary-light: rgba(59, 130, 246, 0.1);
}