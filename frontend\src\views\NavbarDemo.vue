<template>
  <div class="navbar-demo">
    <el-card class="demo-card">
      <template #header>
        <h2>导航栏系统演示</h2>
      </template>
      
      <!-- 当前状态信息 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>当前状态</h3>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="当前路由">
                {{ route.path }}
              </el-descriptions-item>
              <el-descriptions-item label="路由名称">
                {{ route.name || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否管理路由">
                <el-tag :type="isAdminRoute ? 'success' : 'info'">
                  {{ isAdminRoute ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户登录状态">
                <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
                  {{ authStore.isAuthenticated ? '已登录' : '未登录' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="管理权限">
                <el-tag :type="hasAdminAccess ? 'success' : 'warning'">
                  {{ hasAdminAccess ? '有权限' : '无权限' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>导航栏类型</h3>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="当前显示">
                <el-tag :type="currentNavbarType === 'admin' ? 'primary' : 'success'">
                  {{ currentNavbarType === 'admin' ? '管理后台导航' : '前台导航' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="切换逻辑">
                {{ navbarSwitchReason }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 导航测试按钮 -->
      <el-card class="mb-4">
        <template #header>
          <h3>导航测试</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <h4>前台页面</h4>
            <el-space direction="vertical" style="width: 100%">
              <el-button @click="navigateTo('/')" type="primary" plain>
                首页
              </el-button>
              <el-button @click="navigateTo('/article')" type="primary" plain>
                文章列表
              </el-button>
              <el-button @click="navigateTo('/posts')" type="primary" plain>
                说说列表
              </el-button>
              <el-button @click="navigateTo('/category')" type="primary" plain>
                分类页面
              </el-button>
            </el-space>
          </el-col>
          
          <el-col :span="8">
            <h4>管理后台页面</h4>
            <el-space direction="vertical" style="width: 100%">
              <el-button @click="navigateTo('/admin')" type="warning" plain>
                管理首页
              </el-button>
              <el-button @click="navigateTo('/admin/articles')" type="warning" plain>
                文章管理
              </el-button>
              <el-button @click="navigateTo('/admin/users')" type="warning" plain>
                用户管理
              </el-button>
              <el-button @click="navigateTo('/settings')" type="warning" plain>
                系统设置
              </el-button>
            </el-space>
          </el-col>
          
          <el-col :span="8">
            <h4>用户操作</h4>
            <el-space direction="vertical" style="width: 100%">
              <el-button 
                v-if="!authStore.isAuthenticated" 
                @click="navigateTo('/login')" 
                type="success" 
                plain
              >
                登录
              </el-button>
              <el-button 
                v-else 
                @click="handleLogout" 
                type="danger" 
                plain
              >
                退出登录
              </el-button>
              <el-button @click="navigateTo('/profile')" type="info" plain>
                个人资料
              </el-button>
              <el-button @click="navigateTo('/my/articles')" type="info" plain>
                我的文章
              </el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

      <!-- 权限信息 -->
      <el-card v-if="authStore.isAuthenticated">
        <template #header>
          <h3>用户权限信息</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>用户角色</h4>
            <el-space wrap>
              <el-tag 
                v-for="role in userRoles" 
                :key="role" 
                :type="getRoleTagType(role)"
                size="large"
              >
                {{ getRoleDisplayName(role) }}
              </el-tag>
              <el-tag v-if="userRoles.length === 0" type="info">
                暂无角色
              </el-tag>
            </el-space>
          </el-col>
          
          <el-col :span="12">
            <h4>权限统计</h4>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="权限总数">
                {{ userPermissions.length }}
              </el-descriptions-item>
              <el-descriptions-item label="管理权限">
                {{ hasAdminAccess ? '有' : '无' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-card>

      <!-- 开发信息 -->
      <el-card class="mt-4">
        <template #header>
          <h3>开发信息</h3>
        </template>
        
        <el-alert
          title="导航栏切换逻辑说明"
          type="info"
          :closable="false"
        >
          <p>1. 当访问 /admin、/settings 或 /my/audit-logs 路由时，显示管理后台导航栏</p>
          <p>2. 管理后台导航栏需要用户有管理权限（super_admin、admin、editor角色）</p>
          <p>3. 如果用户无管理权限但访问管理路由，仍显示前台导航栏</p>
          <p>4. 前台导航栏适用于所有其他页面</p>
        </el-alert>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalMenu } from '@/composables/useMenu'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const { hasAdminAccess, userRoles, userPermissions } = useGlobalMenu()

// 判断当前是否在管理后台路由
const isAdminRoute = computed(() => {
  return route.path.startsWith('/admin') || 
         route.path.startsWith('/settings') ||
         route.name === 'MyAuditLogs'
})

// 当前导航栏类型
const currentNavbarType = computed(() => {
  return isAdminRoute.value && hasAdminAccess.value ? 'admin' : 'frontend'
})

// 导航栏切换原因
const navbarSwitchReason = computed(() => {
  if (!isAdminRoute.value) {
    return '当前为前台路由，显示前台导航'
  }
  
  if (isAdminRoute.value && hasAdminAccess.value) {
    return '当前为管理路由且有管理权限，显示管理导航'
  }
  
  if (isAdminRoute.value && !hasAdminAccess.value) {
    return '当前为管理路由但无管理权限，显示前台导航'
  }
  
  return '未知状态'
})

// 导航到指定路由
const navigateTo = (path: string) => {
  router.push(path)
}

// 处理用户登出
const handleLogout = () => {
  authStore.logout()
  router.push('/')
}

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'super_admin': return 'danger'
    case 'admin': return 'warning'
    case 'editor': return 'primary'
    case 'user': return 'info'
    default: return 'info'
  }
}

// 获取角色显示名称
const getRoleDisplayName = (role: string) => {
  switch (role) {
    case 'super_admin': return '超级管理员'
    case 'admin': return '管理员'
    case 'editor': return '编辑者'
    case 'user': return '用户'
    default: return role
  }
}
</script>

<style scoped>
.navbar-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-card {
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

h2 {
  margin: 0;
  color: var(--color-text-primary);
}

h3 {
  margin: 0 0 16px 0;
  color: var(--color-text-secondary);
  font-size: 16px;
}

h4 {
  margin: 0 0 12px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.el-alert p {
  margin: 4px 0;
  font-size: 14px;
}
</style>
